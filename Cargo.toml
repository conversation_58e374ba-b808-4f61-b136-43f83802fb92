[package]
name = "temp-cache-cleaner"
version = "0.1.0"
edition = "2021"

[dependencies]
eframe = "0.28"
egui = "0.28"
walkdir = "2.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sysinfo = "0.30"

[build-dependencies]
winresource = "0.1.22"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_Environment",
] }

[[bin]]
name = "temp-cache-cleaner"
path = "src/main.rs"

[profile.release]
strip = true
lto = true
codegen-units = 1
panic = "abort"
