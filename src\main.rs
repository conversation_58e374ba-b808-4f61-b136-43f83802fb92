#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use eframe::egui;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use std::process::Command;
use std::sync::mpsc;
use std::thread;
use sysinfo::{Pid, System};
use walkdir::WalkDir;

fn main() -> Result<(), eframe::Error> {
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([500.0, 600.0])
            .with_title("System Optimizer & Cleaner")
            .with_resizable(true),
        ..Default::default()
    };

    eframe::run_native(
        "System Optimizer & Cleaner",
        options,
        Box::new(|_cc| Ok(Box::new(SystemOptimizerApp::new()))),
    )
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct Settings {
    // Cleaning options
    clean_system_temp: bool,
    clean_user_temp: bool,
    clean_windows_temp: bool,
    clean_chrome_cache: bool,
    clean_firefox_cache: bool,
    clean_edge_cache: bool,

    // Streaming optimization options
    optimize_network: bool,
    optimize_dns: bool,
    optimize_system_resources: bool,
    close_high_cpu_processes: bool,

    // App options
    auto_close_after_clean: bool,
    show_detailed_log: bool,
    cpu_threshold: f32,
}

impl Default for Settings {
    fn default() -> Self {
        Self {
            clean_system_temp: true,
            clean_user_temp: true,
            clean_windows_temp: true,
            clean_chrome_cache: true,
            clean_firefox_cache: true,
            clean_edge_cache: true,
            optimize_network: true,
            optimize_dns: true,
            optimize_system_resources: true,
            close_high_cpu_processes: false,
            auto_close_after_clean: false,
            show_detailed_log: true,
            cpu_threshold: 20.0,
        }
    }
}

#[derive(Debug, Clone)]
struct TipContent {
    title: String,
    content: String,
    icon: String,
}

struct SystemOptimizerApp {
    cleaning_in_progress: bool,
    log_messages: Vec<String>,
    bytes_cleaned: u64,
    files_deleted: u32,
    processes_optimized: u32,
    receiver: Option<mpsc::Receiver<OptimizationMessage>>,
    settings: Settings,
    show_settings: bool,
    current_operation: String,
    progress: f32,
    total_operations: usize,
    completed_operations: usize,

    // Modal states
    show_completion_modal: bool,
    show_tips_modal: bool,
    show_process_modal: bool,
    current_tip_index: usize,
    tips: Vec<TipContent>,
    high_cpu_processes: Vec<ProcessInfo>,

    // System monitoring
    system: System,
    network_stats: Vec<NetworkStat>,
}

#[derive(Debug, Clone)]
struct ProcessInfo {
    pid: u32,
    name: String,
    cpu_usage: f32,
    memory: u64,
}

#[derive(Debug, Clone)]
struct NetworkStat {
    interface: String,
    received: u64,
    transmitted: u64,
}

#[derive(Debug)]
enum OptimizationMessage {
    Log(String),
    Progress(u64, u32, u32), // bytes, files, processes
    OperationUpdate(String, f32),
    ProcessFound(Vec<ProcessInfo>),
    NetworkOptimized,
    Finished,
}

impl SystemOptimizerApp {
    fn new() -> Self {
        let settings = Self::load_settings();
        let tips = Self::create_tips();

        Self {
            cleaning_in_progress: false,
            log_messages: Vec::new(),
            bytes_cleaned: 0,
            files_deleted: 0,
            processes_optimized: 0,
            receiver: None,
            settings,
            show_settings: false,
            current_operation: String::new(),
            progress: 0.0,
            total_operations: 0,
            completed_operations: 0,
            show_completion_modal: false,
            show_tips_modal: false,
            show_process_modal: false,
            current_tip_index: 0,
            tips,
            high_cpu_processes: Vec::new(),
            system: System::new_all(),
            network_stats: Vec::new(),
        }
    }

    fn create_tips() -> Vec<TipContent> {
        vec![
            TipContent {
                title: "Browser Optimization".to_string(),
                icon: "🌐".to_string(),
                content: "• Close unnecessary browser tabs\n• Disable auto-play videos\n• Use ad blockers to reduce bandwidth\n• Clear cookies and browsing data regularly".to_string(),
            },
            TipContent {
                title: "Network Connection".to_string(),
                icon: "📶".to_string(),
                content: "• Use ethernet instead of Wi-Fi when possible\n• Position router in central location\n• Avoid interference from other devices\n• Update network drivers regularly".to_string(),
            },
            TipContent {
                title: "Streaming Quality".to_string(),
                icon: "🎬".to_string(),
                content: "• Lower video quality if experiencing buffering\n• Use appropriate resolution for your screen\n• Close other streaming applications\n• Check your internet speed requirements".to_string(),
            },
            TipContent {
                title: "System Performance".to_string(),
                icon: "⚡".to_string(),
                content: "• Keep your system updated\n• Run disk cleanup regularly\n• Monitor CPU and memory usage\n• Restart your computer periodically".to_string(),
            },
            TipContent {
                title: "Background Applications".to_string(),
                icon: "🔧".to_string(),
                content: "• Close unnecessary background apps\n• Disable startup programs you don't need\n• Use Task Manager to monitor resource usage\n• Consider using Game Mode for better performance".to_string(),
            },
        ]
    }

    fn load_settings() -> Settings {
        let settings_path = Self::get_settings_path();
        if let Ok(contents) = fs::read_to_string(&settings_path) {
            serde_json::from_str(&contents).unwrap_or_default()
        } else {
            Settings::default()
        }
    }

    fn save_settings(&self) {
        let settings_path = Self::get_settings_path();
        if let Ok(json) = serde_json::to_string_pretty(&self.settings) {
            let _ = fs::write(&settings_path, json);
        }
    }

    fn get_settings_path() -> PathBuf {
        let mut path = std::env::current_exe().unwrap_or_default();
        path.set_file_name("settings.json");
        path
    }

    fn start_optimization(&mut self) {
        if self.cleaning_in_progress {
            return;
        }

        self.cleaning_in_progress = true;
        self.log_messages.clear();
        self.bytes_cleaned = 0;
        self.files_deleted = 0;
        self.processes_optimized = 0;
        self.progress = 0.0;
        self.completed_operations = 0;
        self.current_operation = "Initializing optimization...".to_string();

        // Count total operations
        self.total_operations = 0;
        if self.settings.clean_system_temp {
            self.total_operations += 1;
        }
        if self.settings.clean_user_temp {
            self.total_operations += 1;
        }
        if self.settings.clean_windows_temp {
            self.total_operations += 1;
        }
        if self.settings.clean_chrome_cache {
            self.total_operations += 1;
        }
        if self.settings.clean_firefox_cache {
            self.total_operations += 1;
        }
        if self.settings.clean_edge_cache {
            self.total_operations += 1;
        }
        if self.settings.optimize_network {
            self.total_operations += 1;
        }
        if self.settings.optimize_dns {
            self.total_operations += 1;
        }
        if self.settings.optimize_system_resources {
            self.total_operations += 1;
        }

        let (tx, rx) = mpsc::channel();
        self.receiver = Some(rx);

        let settings = self.settings.clone();
        thread::spawn(move || {
            let optimizer = SystemOptimizer::new(tx, settings);
            optimizer.run();
        });
    }

    fn check_messages(&mut self) {
        let mut messages_to_process = Vec::new();
        let mut should_finish = false;

        if let Some(receiver) = &self.receiver {
            while let Ok(msg) = receiver.try_recv() {
                match msg {
                    OptimizationMessage::Finished => {
                        should_finish = true;
                        break;
                    }
                    other => messages_to_process.push(other),
                }
            }
        }

        for msg in messages_to_process {
            match msg {
                OptimizationMessage::Log(message) => {
                    if self.settings.show_detailed_log {
                        self.log_messages.push(message);
                    }
                }
                OptimizationMessage::Progress(bytes, files, processes) => {
                    self.bytes_cleaned += bytes;
                    self.files_deleted += files;
                    self.processes_optimized += processes;
                }
                OptimizationMessage::OperationUpdate(operation, progress) => {
                    self.current_operation = operation;
                    if progress >= 1.0 {
                        self.completed_operations += 1;
                    }
                    self.progress = (self.completed_operations as f32 + progress)
                        / self.total_operations as f32;
                }
                OptimizationMessage::ProcessFound(processes) => {
                    self.high_cpu_processes = processes;
                    if !self.high_cpu_processes.is_empty() {
                        self.show_process_modal = true;
                    }
                }
                OptimizationMessage::NetworkOptimized => {
                    self.update_network_stats();
                }
                OptimizationMessage::Finished => {}
            }
        }

        if should_finish {
            self.cleaning_in_progress = false;
            self.receiver = None;
            self.current_operation = "Optimization completed!".to_string();
            self.progress = 1.0;
            self.show_completion_modal = true;
        }
    }

    fn update_network_stats(&mut self) {
        self.system.refresh_networks_list();
        self.network_stats.clear();

        for (interface_name, network) in self.system.networks() {
            self.network_stats.push(NetworkStat {
                interface: interface_name.clone(),
                received: network.total_received(),
                transmitted: network.total_transmitted(),
            });
        }
    }

    fn draw_progress_bar(&self, ui: &mut egui::Ui) {
        let desired_size = egui::vec2(ui.available_width(), 25.0);
        let (rect, _response) = ui.allocate_exact_size(desired_size, egui::Sense::hover());

        if ui.is_rect_visible(rect) {
            let visuals = ui.style().visuals.clone();

            // Background
            ui.painter()
                .rect_filled(rect, egui::Rounding::same(6.0), visuals.extreme_bg_color);

            // Progress fill
            if self.progress > 0.0 {
                let fill_width = rect.width() * self.progress;
                let fill_rect =
                    egui::Rect::from_min_size(rect.min, egui::vec2(fill_width, rect.height()));

                let progress_color = if self.cleaning_in_progress {
                    egui::Color32::from_rgb(76, 175, 80)
                } else {
                    egui::Color32::from_rgb(33, 150, 243)
                };

                ui.painter()
                    .rect_filled(fill_rect, egui::Rounding::same(6.0), progress_color);
            }

            // Border
            ui.painter().rect_stroke(
                rect,
                egui::Rounding::same(6.0),
                egui::Stroke::new(1.0, visuals.widgets.noninteractive.bg_stroke.color),
            );

            // Progress text
            let progress_text = format!("{:.0}%", self.progress * 100.0);
            let text_color = if self.progress > 0.5 {
                egui::Color32::WHITE
            } else {
                visuals.text_color()
            };

            ui.painter().text(
                rect.center(),
                egui::Align2::CENTER_CENTER,
                &progress_text,
                egui::FontId::monospace(14.0),
                text_color,
            );
        }
    }

    fn show_completion_modal(&mut self, ctx: &egui::Context) {
        if !self.show_completion_modal {
            return;
        }

        egui::Area::new("completion_modal".into())
            .fixed_pos(egui::pos2(0.0, 0.0))
            .show(ctx, |ui| {
                let screen_rect = ctx.screen_rect();

                // Semi-transparent backdrop
                ui.painter().rect_filled(
                    screen_rect,
                    egui::Rounding::ZERO,
                    egui::Color32::from_black_alpha(128),
                );

                // Modal dialog
                egui::Window::new("🎉 Optimization Complete!")
                    .anchor(egui::Align2::CENTER_CENTER, egui::vec2(0.0, 0.0))
                    .resizable(false)
                    .collapsible(false)
                    .show(ctx, |ui| {
                        ui.vertical_centered(|ui| {
                            ui.add_space(10.0);

                            ui.label(
                                egui::RichText::new(
                                    "✅ System optimization completed successfully!",
                                )
                                .size(16.0)
                                .color(egui::Color32::from_rgb(76, 175, 80)),
                            );

                            ui.add_space(15.0);

                            ui.horizontal(|ui| {
                                ui.label("📁 Files cleaned:");
                                ui.label(
                                    egui::RichText::new(format!("{}", self.files_deleted))
                                        .color(egui::Color32::from_rgb(33, 150, 243)),
                                );
                            });

                            ui.horizontal(|ui| {
                                ui.label("💾 Space freed:");
                                ui.label(
                                    egui::RichText::new(format!(
                                        "{:.2} MB",
                                        self.bytes_cleaned as f64 / 1024.0 / 1024.0
                                    ))
                                    .color(egui::Color32::from_rgb(33, 150, 243)),
                                );
                            });

                            ui.horizontal(|ui| {
                                ui.label("⚡ Processes optimized:");
                                ui.label(
                                    egui::RichText::new(format!("{}", self.processes_optimized))
                                        .color(egui::Color32::from_rgb(33, 150, 243)),
                                );
                            });

                            ui.add_space(20.0);

                            ui.horizontal(|ui| {
                                if ui.button("💡 Show Tips").clicked() {
                                    self.show_completion_modal = false;
                                    self.show_tips_modal = true;
                                    self.current_tip_index = 0;
                                }

                                if ui.button("✅ Close").clicked() {
                                    self.show_completion_modal = false;
                                    if self.settings.auto_close_after_clean {
                                        std::process::exit(0);
                                    }
                                }
                            });

                            ui.add_space(10.0);
                        });
                    });
            });
    }

    fn show_tips_modal(&mut self, ctx: &egui::Context) {
        if !self.show_tips_modal {
            return;
        }

        egui::Area::new("tips_modal".into())
            .fixed_pos(egui::pos2(0.0, 0.0))
            .show(ctx, |ui| {
                let screen_rect = ctx.screen_rect();

                // Semi-transparent backdrop
                ui.painter().rect_filled(
                    screen_rect,
                    egui::Rounding::ZERO,
                    egui::Color32::from_black_alpha(128),
                );

                // Modal dialog
                let current_tip = &self.tips[self.current_tip_index];
                egui::Window::new(format!("{} {}", current_tip.icon, current_tip.title))
                    .anchor(egui::Align2::CENTER_CENTER, egui::vec2(0.0, 0.0))
                    .resizable(false)
                    .collapsible(false)
                    .show(ctx, |ui| {
                        ui.set_min_width(400.0);

                        ui.vertical_centered(|ui| {
                            ui.add_space(10.0);

                            ui.label(format!(
                                "Tip {} of {}",
                                self.current_tip_index + 1,
                                self.tips.len()
                            ));

                            ui.add_space(10.0);

                            ui.label(egui::RichText::new(&current_tip.content).size(14.0));

                            ui.add_space(20.0);

                            ui.horizontal(|ui| {
                                if ui
                                    .add_enabled(
                                        self.current_tip_index > 0,
                                        egui::Button::new("⬅ Previous"),
                                    )
                                    .clicked()
                                {
                                    self.current_tip_index -= 1;
                                }

                                ui.with_layout(
                                    egui::Layout::right_to_left(egui::Align::Center),
                                    |ui| {
                                        if ui.button("✅ Done").clicked() {
                                            self.show_tips_modal = false;
                                            if self.settings.auto_close_after_clean {
                                                std::process::exit(0);
                                            }
                                        }

                                        if ui
                                            .add_enabled(
                                                self.current_tip_index < self.tips.len() - 1,
                                                egui::Button::new("Next ➡"),
                                            )
                                            .clicked()
                                        {
                                            self.current_tip_index += 1;
                                        }
                                    },
                                );
                            });

                            ui.add_space(10.0);
                        });
                    });
            });
    }

    fn show_process_modal(&mut self, ctx: &egui::Context) {
        if !self.show_process_modal {
            return;
        }

        egui::Area::new("process_modal".into())
            .fixed_pos(egui::pos2(0.0, 0.0))
            .show(ctx, |ui| {
                let screen_rect = ctx.screen_rect();

                ui.painter().rect_filled(
                    screen_rect,
                    egui::Rounding::ZERO,
                    egui::Color32::from_black_alpha(128),
                );

                egui::Window::new("⚠️ High CPU Usage Detected")
                    .anchor(egui::Align2::CENTER_CENTER, egui::vec2(0.0, 0.0))
                    .resizable(false)
                    .collapsible(false)
                    .show(ctx, |ui| {
                        ui.set_min_width(500.0);

                        ui.label("The following processes are using high CPU resources:");
                        ui.add_space(10.0);

                        egui::ScrollArea::vertical()
                            .max_height(200.0)
                            .show(ui, |ui| {
                                for process in &self.high_cpu_processes {
                                    ui.horizontal(|ui| {
                                        ui.label(format!("📋 {}", process.name));
                                        ui.with_layout(
                                            egui::Layout::right_to_left(egui::Align::Center),
                                            |ui| {
                                                ui.label(format!("{:.1}% CPU", process.cpu_usage));
                                                ui.label(format!(
                                                    "{:.1} MB",
                                                    process.memory as f64 / 1024.0 / 1024.0
                                                ));
                                            },
                                        );
                                    });
                                }
                            });

                        ui.add_space(15.0);

                        ui.horizontal(|ui| {
                            if ui.button("🔧 Optimize Automatically").clicked() {
                                // This would terminate non-essential processes
                                // Implementation depends on your specific needs
                                self.show_process_modal = false;
                            }

                            if ui.button("👁️ Monitor Only").clicked() {
                                self.show_process_modal = false;
                            }
                        });
                    });
            });
    }
}

impl eframe::App for SystemOptimizerApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        self.check_messages();

        if self.cleaning_in_progress {
            ctx.request_repaint();
        }

        // Show modals
        self.show_completion_modal(ctx);
        self.show_tips_modal(ctx);
        self.show_process_modal(ctx);

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("🚀 System Optimizer & Cleaner");
            ui.separator();

            // Settings button
            ui.horizontal(|ui| {
                if ui.button("⚙ Settings").clicked() {
                    self.show_settings = !self.show_settings;
                }
                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    ui.label(format!("v{}", env!("CARGO_PKG_VERSION")));
                });
            });

            // Settings panel
            if self.show_settings {
                ui.separator();
                egui::CollapsingHeader::new("🧹 Cleaning Options")
                    .default_open(true)
                    .show(ui, |ui| {
                        ui.checkbox(&mut self.settings.clean_system_temp, "Clean System Temp");
                        ui.checkbox(&mut self.settings.clean_user_temp, "Clean User Temp");
                        ui.checkbox(&mut self.settings.clean_windows_temp, "Clean Windows Temp");
                        ui.checkbox(&mut self.settings.clean_chrome_cache, "Clean Chrome Cache");
                        ui.checkbox(
                            &mut self.settings.clean_firefox_cache,
                            "Clean Firefox Cache",
                        );
                        ui.checkbox(&mut self.settings.clean_edge_cache, "Clean Edge Cache");
                    });

                egui::CollapsingHeader::new("🎬 Streaming Optimization")
                    .default_open(true)
                    .show(ui, |ui| {
                        ui.checkbox(
                            &mut self.settings.optimize_network,
                            "Optimize Network Settings",
                        );
                        ui.checkbox(&mut self.settings.optimize_dns, "Optimize DNS Settings");
                        ui.checkbox(
                            &mut self.settings.optimize_system_resources,
                            "Optimize System Resources",
                        );
                        ui.checkbox(
                            &mut self.settings.close_high_cpu_processes,
                            "Close High CPU Processes",
                        );
                        ui.horizontal(|ui| {
                            ui.label("CPU Threshold:");
                            ui.add(
                                egui::Slider::new(&mut self.settings.cpu_threshold, 5.0..=50.0)
                                    .suffix("%"),
                            );
                        });
                    });

                egui::CollapsingHeader::new("🔧 Application Options")
                    .default_open(true)
                    .show(ui, |ui| {
                        ui.checkbox(
                            &mut self.settings.auto_close_after_clean,
                            "Auto-close after optimization",
                        );
                        ui.checkbox(&mut self.settings.show_detailed_log, "Show detailed log");
                    });

                ui.horizontal(|ui| {
                    if ui.button("💾 Save Settings").clicked() {
                        self.save_settings();
                    }
                    if ui.button("🔄 Reset to Default").clicked() {
                        self.settings = Settings::default();
                        self.save_settings();
                    }
                });
                ui.separator();
            }

            // Status and progress
            ui.vertical_centered(|ui| {
                ui.label(&self.current_operation);
                ui.add_space(8.0);
                self.draw_progress_bar(ui);
                ui.add_space(15.0);
            });

            // Statistics
            ui.horizontal(|ui| {
                ui.label(format!("📄 Files: {}", self.files_deleted));
                ui.separator();
                ui.label(format!(
                    "💾 Space: {:.2} MB",
                    self.bytes_cleaned as f64 / 1024.0 / 1024.0
                ));
                ui.separator();
                ui.label(format!("⚡ Processes: {}", self.processes_optimized));
                ui.separator();
                ui.label(format!(
                    "📊 Progress: {}/{}",
                    self.completed_operations, self.total_operations
                ));
            });

            ui.separator();

            // Start button
            ui.vertical_centered(|ui| {
                let button_text = if self.cleaning_in_progress {
                    "🔄 Optimizing System..."
                } else {
                    "🚀 Start Optimization"
                };

                if ui
                    .add_enabled(
                        !self.cleaning_in_progress,
                        egui::Button::new(button_text).min_size(egui::vec2(220.0, 40.0)),
                    )
                    .clicked()
                {
                    self.start_optimization();
                }
            });

            // Log area
            if self.settings.show_detailed_log {
                ui.separator();
                ui.label("📋 Optimization Log:");
                egui::ScrollArea::vertical()
                    .max_height(150.0)
                    .auto_shrink([false; 2])
                    .show(ui, |ui| {
                        for message in &self.log_messages {
                            ui.label(message);
                        }
                    });
            }

            // Network stats (if available)
            if !self.network_stats.is_empty() && !self.cleaning_in_progress {
                ui.separator();
                ui.label("📡 Network Statistics:");
                for stat in &self.network_stats {
                    ui.horizontal(|ui| {
                        ui.label(format!("Interface: {}", stat.interface));
                        ui.label(format!(
                            "↓ {:.2} MB",
                            stat.received as f64 / 1024.0 / 1024.0
                        ));
                        ui.label(format!(
                            "↑ {:.2} MB",
                            stat.transmitted as f64 / 1024.0 / 1024.0
                        ));
                    });
                }
            }
        });
    }
}

struct SystemOptimizer {
    sender: mpsc::Sender<OptimizationMessage>,
    settings: Settings,
    system: System,
}

impl SystemOptimizer {
    fn new(sender: mpsc::Sender<OptimizationMessage>, settings: Settings) -> Self {
        Self {
            sender,
            settings,
            system: System::new_all(),
        }
    }

    fn log(&self, message: &str) {
        let _ = self
            .sender
            .send(OptimizationMessage::Log(message.to_string()));
    }

    fn report_progress(&self, bytes: u64, files: u32, processes: u32) {
        let _ = self
            .sender
            .send(OptimizationMessage::Progress(bytes, files, processes));
    }

    fn update_operation(&self, operation: &str, progress: f32) {
        let _ = self.sender.send(OptimizationMessage::OperationUpdate(
            operation.to_string(),
            progress,
        ));
    }

    fn run(&self) {
        self.log("Starting system optimization...");

        // System resource optimization
        if self.settings.optimize_system_resources {
            self.update_operation("Analyzing system resources...", 0.0);
            self.optimize_system_resources();
            self.update_operation("System resources optimized", 1.0);
        }

        // Network optimization
        if self.settings.optimize_network {
            self.update_operation("Optimizing network settings...", 0.0);
            self.optimize_network();
            self.update_operation("Network optimized", 1.0);
        }

        // DNS optimization
        if self.settings.optimize_dns {
            self.update_operation("Optimizing DNS settings...", 0.0);
            self.optimize_dns();
            self.update_operation("DNS optimized", 1.0);
        }

        // Clean temp files
        if self.settings.clean_system_temp {
            self.update_operation("Cleaning system temp...", 0.0);
            self.clean_system_temp();
            self.update_operation("System temp cleaned", 1.0);
        }

        if self.settings.clean_user_temp {
            self.update_operation("Cleaning user temp...", 0.0);
            self.clean_user_temp();
            self.update_operation("User temp cleaned", 1.0);
        }

        if self.settings.clean_windows_temp {
            self.update_operation("Cleaning Windows temp...", 0.0);
            self.clean_windows_temp();
            self.update_operation("Windows temp cleaned", 1.0);
        }

        // Clean browser caches
        if self.settings.clean_chrome_cache {
            self.update_operation("Cleaning Chrome cache...", 0.0);
            self.clean_chrome_cache();
            self.update_operation("Chrome cache cleaned", 1.0);
        }

        if self.settings.clean_firefox_cache {
            self.update_operation("Cleaning Firefox cache...", 0.0);
            self.clean_firefox_cache();
            self.update_operation("Firefox cache cleaned", 1.0);
        }

        if self.settings.clean_edge_cache {
            self.update_operation("Cleaning Edge cache...", 0.0);
            self.clean_edge_cache();
            self.update_operation("Edge cache cleaned", 1.0);
        }

        self.log("System optimization completed!");
        let _ = self.sender.send(OptimizationMessage::NetworkOptimized);
        let _ = self.sender.send(OptimizationMessage::Finished);
    }

    fn optimize_system_resources(&self) {
        self.log("Scanning for high CPU processes...");

        let mut system = System::new_all();
        system.refresh_all();

        let mut high_cpu_processes = Vec::new();
        let mut processes_optimized = 0u32;

        for (pid, process) in system.processes() {
            let cpu_usage = process.cpu_usage();
            if cpu_usage > self.settings.cpu_threshold && !self.is_essential_process(process.name())
            {
                high_cpu_processes.push(ProcessInfo {
                    pid: pid.as_u32(),
                    name: process.name().to_string(),
                    cpu_usage,
                    memory: process.memory(),
                });

                if self.settings.close_high_cpu_processes {
                    // Attempt to terminate process (be careful with this)
                    #[cfg(target_os = "windows")]
                    {
                        let _ = Command::new("taskkill")
                            .args(&["/PID", &pid.as_u32().to_string(), "/F"])
                            .output();
                        processes_optimized += 1;
                    }

                    #[cfg(target_os = "linux")]
                    {
                        let _ = Command::new("kill")
                            .args(&["-9", &pid.as_u32().to_string()])
                            .output();
                        processes_optimized += 1;
                    }
                }
            }
        }

        if !high_cpu_processes.is_empty() {
            self.log(&format!(
                "Found {} high CPU processes",
                high_cpu_processes.len()
            ));
            let _ = self
                .sender
                .send(OptimizationMessage::ProcessFound(high_cpu_processes));
        }

        self.report_progress(0, 0, processes_optimized);
    }

    fn is_essential_process(&self, name: &str) -> bool {
        let essential = [
            "firefox",
            "chrome",
            "edge",
            "safari",
            "opera",
            "brave",
            "system",
            "kernel",
            "dwm.exe",
            "explorer.exe",
            "winlogon.exe",
            "csrss.exe",
            "smss.exe",
            "wininit.exe",
            "services.exe",
            "lsass.exe",
            "svchost.exe",
            "conhost.exe",
            "taskhost.exe",
            "systemd",
            "init",
            "sshd",
            "dbus",
            "networkmanager",
            "pulseaudio",
            "xorg",
            "gdm",
            "lightdm",
            "plasma",
        ];

        essential
            .iter()
            .any(|&essential_name| name.to_lowercase().contains(essential_name))
    }

    fn optimize_network(&self) {
        self.log("Optimizing network settings...");

        #[cfg(target_os = "windows")]
        {
            // Windows network optimizations
            let _ = Command::new("netsh")
                .args(&["int", "tcp", "set", "global", "autotuninglevel=normal"])
                .output();

            let _ = Command::new("netsh")
                .args(&["int", "tcp", "set", "global", "chimney=enabled"])
                .output();

            let _ = Command::new("netsh")
                .args(&["int", "tcp", "set", "global", "rss=enabled"])
                .output();

            let _ = Command::new("netsh")
                .args(&["int", "tcp", "set", "global", "netdma=enabled"])
                .output();

            self.log("Windows network settings optimized");
        }

        #[cfg(target_os = "linux")]
        {
            // Linux network optimizations (requires root)
            let _ = Command::new("sysctl")
                .args(&["-w", "net.core.rmem_max=16777216"])
                .output();

            let _ = Command::new("sysctl")
                .args(&["-w", "net.core.wmem_max=16777216"])
                .output();

            let _ = Command::new("sysctl")
                .args(&["-w", "net.core.rmem_default=262144"])
                .output();

            let _ = Command::new("sysctl")
                .args(&["-w", "net.core.wmem_default=262144"])
                .output();

            let _ = Command::new("sysctl")
                .args(&["-w", "net.core.netdev_max_backlog=5000"])
                .output();

            self.log("Linux network settings optimized");
        }
    }

    fn optimize_dns(&self) {
        self.log("Optimizing DNS settings...");

        #[cfg(target_os = "windows")]
        {
            // Get active network interfaces
            let output = Command::new("netsh")
                .args(&["interface", "show", "interface"])
                .output();

            if let Ok(output) = output {
                let output_str = String::from_utf8_lossy(&output.stdout);

                // Look for connected interfaces
                for line in output_str.lines() {
                    if line.contains("Connected")
                        && (line.contains("Wi-Fi") || line.contains("Ethernet"))
                    {
                        let parts: Vec<&str> = line.split_whitespace().collect();
                        if let Some(interface_name) = parts.last() {
                            // Set primary DNS to Cloudflare (fast for streaming)
                            let _ = Command::new("netsh")
                                .args(&[
                                    "interface",
                                    "ip",
                                    "set",
                                    "dns",
                                    interface_name,
                                    "static",
                                    "*******",
                                ])
                                .output();

                            // Set secondary DNS
                            let _ = Command::new("netsh")
                                .args(&[
                                    "interface",
                                    "ip",
                                    "add",
                                    "dns",
                                    interface_name,
                                    "*******",
                                    "index=2",
                                ])
                                .output();

                            self.log(&format!("DNS optimized for interface: {}", interface_name));
                        }
                    }
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            // Linux DNS optimization
            let dns_config = "nameserver *******\nnameserver *******\n";
            let _ = fs::write("/etc/resolv.conf", dns_config);
            self.log("Linux DNS settings optimized");
        }

        // Flush DNS cache
        #[cfg(target_os = "windows")]
        {
            let _ = Command::new("ipconfig").args(&["/flushdns"]).output();
            self.log("DNS cache flushed");
        }

        #[cfg(target_os = "linux")]
        {
            let _ = Command::new("systemctl")
                .args(&["restart", "systemd-resolved"])
                .output();
            self.log("DNS cache flushed");
        }
    }

    fn clean_system_temp(&self) {
        let temp_dir = std::env::temp_dir();
        if temp_dir.exists() {
            self.log(&format!("Cleaning: {}", temp_dir.display()));
            self.clean_directory(&temp_dir);
        }
    }

    fn clean_user_temp(&self) {
        #[cfg(target_os = "windows")]
        {
            if let Ok(user_profile) = std::env::var("USERPROFILE") {
                let temp_dir = PathBuf::from(format!("{}\\AppData\\Local\\Temp", user_profile));
                if temp_dir.exists() {
                    self.log(&format!("Cleaning: {}", temp_dir.display()));
                    self.clean_directory(&temp_dir);
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            if let Ok(home) = std::env::var("HOME") {
                let temp_dir = PathBuf::from(format!("{}/.cache", home));
                if temp_dir.exists() {
                    self.log(&format!("Cleaning: {}", temp_dir.display()));
                    self.clean_directory(&temp_dir);
                }
            }
        }
    }

    fn clean_windows_temp(&self) {
        #[cfg(target_os = "windows")]
        {
            let temp_dir = PathBuf::from("C:\\Windows\\Temp");
            if temp_dir.exists() {
                self.log(&format!("Cleaning: {}", temp_dir.display()));
                self.clean_directory(&temp_dir);
            }
        }
    }

    fn clean_chrome_cache(&self) {
        #[cfg(target_os = "windows")]
        {
            if let Ok(user_profile) = std::env::var("USERPROFILE") {
                let cache_paths = vec![
                    format!(
                        "{}\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache",
                        user_profile
                    ),
                    format!(
                        "{}\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Code Cache",
                        user_profile
                    ),
                    format!(
                        "{}\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\GPUCache",
                        user_profile
                    ),
                ];

                for path in cache_paths {
                    let path_buf = PathBuf::from(&path);
                    if path_buf.exists() {
                        self.log(&format!("Cleaning Chrome: {}", path));
                        self.clean_directory(&path_buf);
                    }
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            if let Ok(home) = std::env::var("HOME") {
                let cache_path = PathBuf::from(format!("{}/.cache/google-chrome", home));
                if cache_path.exists() {
                    self.log(&format!("Cleaning Chrome: {}", cache_path.display()));
                    self.clean_directory(&cache_path);
                }
            }
        }
    }

    fn clean_firefox_cache(&self) {
        #[cfg(target_os = "windows")]
        {
            if let Ok(user_profile) = std::env::var("USERPROFILE") {
                let profiles_path = PathBuf::from(format!(
                    "{}\\AppData\\Local\\Mozilla\\Firefox\\Profiles",
                    user_profile
                ));
                if profiles_path.exists() {
                    self.log(&format!("Cleaning Firefox: {}", profiles_path.display()));
                    self.clean_firefox_profiles(&profiles_path);
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            if let Ok(home) = std::env::var("HOME") {
                let profiles_path = PathBuf::from(format!("{}/.mozilla/firefox", home));
                if profiles_path.exists() {
                    self.log(&format!("Cleaning Firefox: {}", profiles_path.display()));
                    self.clean_firefox_profiles(&profiles_path);
                }
            }
        }
    }

    fn clean_edge_cache(&self) {
        #[cfg(target_os = "windows")]
        {
            if let Ok(user_profile) = std::env::var("USERPROFILE") {
                let cache_paths = vec![
                    format!(
                        "{}\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Cache",
                        user_profile
                    ),
                    format!(
                        "{}\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Code Cache",
                        user_profile
                    ),
                    format!(
                        "{}\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\GPUCache",
                        user_profile
                    ),
                ];

                for path in cache_paths {
                    let path_buf = PathBuf::from(&path);
                    if path_buf.exists() {
                        self.log(&format!("Cleaning Edge: {}", path));
                        self.clean_directory(&path_buf);
                    }
                }
            }
        }
    }

    fn clean_directory(&self, dir: &PathBuf) {
        let mut total_bytes = 0u64;
        let mut total_files = 0u32;

        // First pass: delete files
        for entry in WalkDir::new(dir)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.path().is_file())
        {
            let path = entry.path();

            match fs::metadata(path) {
                Ok(metadata) => {
                    let size = metadata.len();
                    match fs::remove_file(path) {
                        Ok(_) => {
                            total_bytes += size;
                            total_files += 1;
                        }
                        Err(_) => {
                            // File might be in use, skip silently
                        }
                    }
                }
                Err(_) => continue,
            }
        }

        // Second pass: try to remove empty directories
        let mut dirs_to_remove = Vec::new();
        for entry in WalkDir::new(dir)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.path().is_dir() && e.path() != dir)
        {
            dirs_to_remove.push(entry.path().to_path_buf());
        }

        // Sort by depth (deepest first)
        dirs_to_remove.sort_by(|a, b| b.components().count().cmp(&a.components().count()));

        for dir_path in dirs_to_remove {
            let _ = fs::remove_dir(&dir_path);
        }

        if total_files > 0 {
            self.report_progress(total_bytes, total_files, 0);
            self.log(&format!(
                "Cleaned {} files ({:.2} MB)",
                total_files,
                total_bytes as f64 / 1024.0 / 1024.0
            ));
        }
    }

    fn clean_firefox_profiles(&self, profiles_dir: &PathBuf) {
        if let Ok(entries) = fs::read_dir(profiles_dir) {
            for entry in entries.filter_map(|e| e.ok()) {
                let profile_path = entry.path();
                if profile_path.is_dir() {
                    let cache_paths = vec![
                        profile_path.join("cache2"),
                        profile_path.join("startupCache"),
                        profile_path.join("thumbnails"),
                    ];

                    for cache_path in cache_paths {
                        if cache_path.exists() {
                            self.clean_directory(&cache_path);
                        }
                    }
                }
            }
        }
    }
}
