// build.rs
use std::env;
use std::io;
use winresource::WindowsResource;

fn main() -> io::Result<()> {
    // Only run this build script on Windows targets
    if env::var("CARGO_CFG_TARGET_OS").unwrap() == "windows" {
        let mut res = WindowsResource::new();
        // Set the path to your .ico file relative to the crate root (where Cargo.toml is)
        res.set_icon("assets/boost.ico"); 
        // You can also set other version info fields if needed
        // res.set("FileDescription", "My Awesome Application");
        // res.set("ProductName", "My App");
        // res.set_version_info(winresource::VersionInfo::PRODUCTVERSION, 0x0001000000000000); // *******

        res.compile()?;
    }
    Ok(())
}